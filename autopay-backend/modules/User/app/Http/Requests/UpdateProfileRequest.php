<?php

namespace Modules\User\Http\Requests;

use Illuminate\Validation\Rule;
use Modules\Core\Http\Requests\BaseFormRequest;
use Modules\User\Models\DeletedUser;

class UpdateProfileRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'name' => [
                'sometimes',
                'min:1',
                'max:32',
            ],
            'first_name' => [
                'sometimes',
                'nullable',
                'string',
                'min:1',
                'max:50',
                'regex:/^[\p{L}\s\-\'\.]+$/u', // Letters, spaces, hyphens, apostrophes, dots
            ],
            'last_name' => [
                'sometimes',
                'nullable',
                'string',
                'min:1',
                'max:50',
                'regex:/^[\p{L}\s\-\'\.]+$/u', // Letters, spaces, hyphens, apostrophes, dots
            ],
            'email' => [
                'sometimes',
                'email:rfc,dns',
                Rule::unique('users', 'email')->ignore($this->user()->id),
                function ($attribute, $value, $fail): void {
                    // Check if this user email was deleted from our system
                    $userDeleted = DeletedUser::where('email_hashed', md5((string) $this->input('email')))->exists();

                    if ($userDeleted) {
                        $fail('This '.$attribute.' is not available.');
                    }
                },
            ],
            'phone' => [
                'sometimes',
                'nullable',
                'string',
                'min:10',
                'max:15',
                'regex:/^[0-9\+\-\s\(\)]+$/',
                Rule::unique('users', 'phone')->ignore($this->user()->id),
            ],
            'timezone' => [
                'sometimes',
                function ($attribute, $value, $fail): void {
                    if (! in_array($value, timezone_identifiers_list(), true)) {
                        $fail('The '.$attribute.' is invalid.');
                    }
                },
            ],
            'current_organization_id' => [
                'sometimes',
                'string',
                'exists:organizations,id',
            ],
            'current_team_id' => [
                'sometimes',
                'string',
                'exists:teams,id',
            ],
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
