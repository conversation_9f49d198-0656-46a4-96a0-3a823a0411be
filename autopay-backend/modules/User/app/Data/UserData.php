<?php

namespace Modules\User\Data;

use <PERSON><PERSON>\LaravelData\Data;
use <PERSON><PERSON>\TypeScriptTransformer\Attributes\TypeScript;

#[TypeScript]
class UserData extends Data
{
    public function __construct(
        public string $id,
        public string $name,
        public ?string $first_name,
        public ?string $last_name,
        public string $email,
        public ?string $phone,
        public ?string $avatar,
        public ?string $timezone,
        public ?array $current_organization,
        public ?array $current_team
    ) {}

    /**
     * Create from the User model
     */
    public static function fromUser($user): self
    {
        return new self(
            id: $user->id,
            name: $user->name,
            first_name: $user->first_name,
            last_name: $user->last_name,
            email: $user->email,
            phone: $user->phone,
            avatar: data_get($user, 'avatar'),
            timezone: data_get($user, 'data.timezone'),
            current_organization: [
                'id' => $user->current_organization_id,
                'name' => $user->currentOrganization->name ?? '',
                'alias' => $user->currentOrganization->alias ?? '',
                'is_owner' => $user->currentOrganization->owner_id === $user->id,
            ],
            current_team: [
                'id' => $user->current_team_id,
                'name' => $user->currentTeam->name ?? '',
                'alias' => $user->currentTeam->alias ?? '',
                'is_owner' => $user->currentTeam->owner_id === $user->id,
            ],
        );
    }
}
