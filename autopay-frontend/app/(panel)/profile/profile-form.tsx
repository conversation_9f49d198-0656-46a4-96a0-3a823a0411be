'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import FormSkeleton from '@/components/display/FormSkeleton'
import { Button } from '@/components/ui/button'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useUser } from '@/lib/hooks/useUser'
import { useUserOrganizations } from '@/lib/hooks/useUserOrganizations'
import { useUserTeams } from '@/lib/hooks/useUserTeams'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import React from 'react'
import { toast } from 'sonner'

const profileFormSchema = z.object({
  name: z
    .string({
      message: 'Tên là bắt buộc',
    })
    .min(1, {
      message: 'Tên tối thiểu là 1 ký tự.',
    })
    .max(32, {
      message: 'Tên không vượt quá 32 ký tự.',
    }),
  first_name: z
    .string()
    .min(1, {
      message: 'Họ tối thiểu là 1 ký tự.',
    })
    .max(50, {
      message: 'Họ không vượt quá 50 ký tự.',
    })
    .regex(/^[\p{L}\s\-'\.]+$/u, {
      message: 'Họ chỉ được chứa chữ cái, khoảng trắng, dấu gạch ngang, dấu nháy và dấu chấm.',
    })
    .optional()
    .or(z.literal('')),
  last_name: z
    .string()
    .min(1, {
      message: 'Tên tối thiểu là 1 ký tự.',
    })
    .max(50, {
      message: 'Tên không vượt quá 50 ký tự.',
    })
    .regex(/^[\p{L}\s\-'\.]+$/u, {
      message: 'Tên chỉ được chứa chữ cái, khoảng trắng, dấu gạch ngang, dấu nháy và dấu chấm.',
    })
    .optional()
    .or(z.literal('')),
  email: z
    .string({
      message: 'Email là bắt buộc',
    })
    .email({
      message: 'Email không hợp lệ',
    }),
  phone: z
    .string()
    .min(10, {
      message: 'Số điện thoại tối thiểu là 10 ký tự.',
    })
    .max(15, {
      message: 'Số điện thoại không vượt quá 15 ký tự.',
    })
    .regex(/^[0-9\+\-\s\(\)]+$/, {
      message: 'Số điện thoại chỉ được chứa số, dấu +, -, khoảng trắng và dấu ngoặc.',
    })
    .optional()
    .or(z.literal('')),
  timezone: z.string().optional(),
  current_organization_id: z.string().optional(),
  current_team_id: z.string().optional(),
})

type ProfileFormValues = z.infer<typeof profileFormSchema>

export function ProfileForm() {
  const { user, isLoading } = useUser()
  const { organizations } = useUserOrganizations()
  const { teams } = useUserTeams(user?.current_organization?.id)
  const queryClient = useQueryClient()

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      name: user?.name || '',
      first_name: user?.first_name || '',
      last_name: user?.last_name || '',
      email: user?.email || '',
      phone: user?.phone || '',
      timezone: user?.timezone || '',
      current_organization_id: user?.current_organization?.id || '',
      current_team_id: user?.current_team?.id || '',
    },
    mode: 'onChange',
  })

  // Reset form when user data is loaded
  React.useEffect(() => {
    if (user) {
      form.reset({
        name: user.name,
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        email: user.email,
        phone: user.phone || '',
        timezone: user.timezone || '',
        current_organization_id: user.current_organization?.id || '',
        current_team_id: user.current_team?.id || '',
      })
    }
  }, [user, form])

  // Update profile mutation
  const updateProfileMutation = useMutation({
    mutationFn: async (data: ProfileFormValues) => {
      // Prepare data for backend
      const updateData: any = {
        name: data.name,
        email: data.email,
      }

      // Add optional fields if provided
      if (data.first_name && data.first_name.trim() !== '') {
        updateData.first_name = data.first_name
      }

      if (data.last_name && data.last_name.trim() !== '') {
        updateData.last_name = data.last_name
      }

      if (data.phone && data.phone.trim() !== '') {
        updateData.phone = data.phone
      }

      if (data.timezone && data.timezone.trim() !== '') {
        updateData.timezone = data.timezone
      }

      if (data.current_organization_id && data.current_organization_id.trim() !== '') {
        updateData.current_organization_id = data.current_organization_id
      }

      if (data.current_team_id && data.current_team_id.trim() !== '') {
        updateData.current_team_id = data.current_team_id
      }

      return queryFetchHelper('/profile', {
        method: 'PATCH',
        body: JSON.stringify(updateData),
      })
    },
    onSuccess: () => {
      toast.success('Cập nhật hồ sơ thành công!')

      // Update user cache
      queryClient.invalidateQueries({ queryKey: ['getUserProfile'] })
      queryClient.invalidateQueries({ queryKey: ['getUserOrganizations'] })
      queryClient.invalidateQueries({ queryKey: ['getUserTeams'] })
    },
    onError: (error: any) => {
      const errorMessage = error?.message || 'Có lỗi xảy ra khi cập nhật hồ sơ'
      toast.error(errorMessage)
    },
  })

  function onSubmit(data: ProfileFormValues) {
    updateProfileMutation.mutate(data)
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <FormSkeleton
          titleWidth="w-24"
          descriptionWidth="w-40"
          className="mb-4"
        />
        <FormSkeleton
          titleWidth="w-16"
          descriptionWidth="w-32"
          buttonWidth="w-32"
        />
      </div>
    )
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Tên hiển thị</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="first_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Họ</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="Nguyễn"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="last_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Tên</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="Văn A"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  type="email"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="phone"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Số điện thoại</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  type="tel"
                  placeholder="0123456789"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="timezone"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Múi giờ</FormLabel>
              <Select
                onValueChange={field.onChange}
                value={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn múi giờ" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="Asia/Ho_Chi_Minh">Việt Nam (UTC+7)</SelectItem>
                  <SelectItem value="Asia/Bangkok">Bangkok (UTC+7)</SelectItem>
                  <SelectItem value="Asia/Singapore">Singapore (UTC+8)</SelectItem>
                  <SelectItem value="Asia/Tokyo">Tokyo (UTC+9)</SelectItem>
                  <SelectItem value="UTC">UTC (UTC+0)</SelectItem>
                  <SelectItem value="America/New_York">New York (UTC-5)</SelectItem>
                  <SelectItem value="Europe/London">London (UTC+0)</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="current_organization_id"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Tổ chức hiện tại</FormLabel>
              <Select
                onValueChange={field.onChange}
                value={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn tổ chức" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {organizations.map((org: any) => (
                    <SelectItem
                      key={org.id}
                      value={org.id}>
                      {org.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="current_team_id"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Nhóm hiện tại</FormLabel>
              <Select
                onValueChange={field.onChange}
                value={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn nhóm" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {teams.map((team: any) => (
                    <SelectItem
                      key={team.team?.id}
                      value={team.team?.id}>
                      {team.team?.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button
          type="submit"
          disabled={updateProfileMutation.isPending}>
          {updateProfileMutation.isPending ? 'Đang cập nhật...' : 'Cập nhật hồ sơ'}
        </Button>
      </form>
    </Form>
  )
}
