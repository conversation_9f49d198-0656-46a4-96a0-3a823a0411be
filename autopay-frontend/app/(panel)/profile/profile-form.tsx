'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import FormSkeleton from '@/components/display/FormSkeleton'
import { Button } from '@/components/ui/button'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { useUser } from '@/lib/hooks/useUser'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import React from 'react'
import { toast } from 'sonner'

const profileFormSchema = z.object({
  name: z
    .string({
      message: 'Tên là bắt buộc',
    })
    .min(1, {
      message: 'Tên tối thiểu là 1 ký tự.',
    })
    .max(32, {
      message: 'Tên không vượt quá 32 ký tự.',
    }),
  email: z
    .string({
      message: '<PERSON><PERSON> là bắt buộc',
    })
    .email({
      message: '<PERSON>ail không hợp lệ',
    }),
})

type ProfileFormValues = z.infer<typeof profileFormSchema>

export function ProfileForm() {
  const { user, isLoading } = useUser()
  const queryClient = useQueryClient()

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      name: user?.name || '',
      email: user?.email || '',
    },
    mode: 'onChange',
  })

  // Reset form when user data is loaded
  React.useEffect(() => {
    if (user) {
      form.reset({
        name: user.name,
        email: user.email,
      })
    }
  }, [user, form])

  // Update profile mutation
  const updateProfileMutation = useMutation({
    mutationFn: async (data: ProfileFormValues) => {
      return queryFetchHelper('/profile', {
        method: 'PATCH',
        body: JSON.stringify(data),
      })
    },
    onSuccess: () => {
      toast.success('Cập nhật hồ sơ thành công!')

      // Update user cache
      queryClient.invalidateQueries({ queryKey: ['getUserProfile'] })
    },
    onError: (error: any) => {
      const errorMessage = error?.message || 'Có lỗi xảy ra khi cập nhật hồ sơ'
      toast.error(errorMessage)
    },
  })

  function onSubmit(data: ProfileFormValues) {
    updateProfileMutation.mutate(data)
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <FormSkeleton
          titleWidth="w-24"
          descriptionWidth="w-40"
          className="mb-4"
        />
        <FormSkeleton
          titleWidth="w-16"
          descriptionWidth="w-32"
          buttonWidth="w-32"
        />
      </div>
    )
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Họ và tên</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button
          type="submit"
          disabled={updateProfileMutation.isPending}>
          {updateProfileMutation.isPending ? 'Đang cập nhật...' : 'Cập nhật hồ sơ'}
        </Button>
      </form>
    </Form>
  )
}
