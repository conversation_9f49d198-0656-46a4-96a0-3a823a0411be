interface User {
  id: string
  name: string
  email: string
  phone?: string
  avatar?: string
  timezone?: string
  two_factor?: {
    enabled?: boolean
    secret_code?: string
  }
  current_organization?: {
    id: string
    alias: string
    is_owner?: boolean
  }
  current_team?: {
    id: string
    alias: string
    is_owner?: boolean
  }

  [key: string]: any
}

interface UserRole {
  id: string
  name: string
  permissions: Permission[]
  organization_id?: string
  team_id?: string
  is_default?: boolean
}
