import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useQuery } from '@tanstack/react-query'

export const useUserTeams = (organizationId?: string) => {
  const { data, isLoading, error } = useQuery<ApiResponse<any[]>>({
    queryKey: ['getUserTeams', organizationId],
    queryFn: () => {
      if (!organizationId) return Promise.resolve({ data: [] })
      return queryFetchHelper(`/${organizationId}/my-teams`)
    },
    enabled: !!organizationId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })

  return {
    teams: data?.data || [],
    isLoading,
    error,
  }
}
